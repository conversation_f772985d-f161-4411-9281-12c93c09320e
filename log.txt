[Preload] Initializing CoreDesk preload bridge...
VM154:151 [Preload] APIs exposed successfully
VM154:120 [Preload] CoreDesk preload bridge initialized successfully
index.html:48 🔒 IMMEDIATE AUTH GATE: Checking BEFORE any content renders...
index.html:55 🔍 IMMEDIATE AUTH GATE: Token in localStorage: true
index.html:56 🔍 IMMEDIATE AUTH GATE: Expiry in localStorage: true
index.html:72 🔍 IMMEDIATE AUTH GATE: Token is valid: true
index.html:88 ✅ IMMEDIATE AUTH GATE: Authenticated - allowing content to render
index.html:500 
        
        
       GET file:///C:/Users/<USER>/AppData/Local/Programs/CoreDesk%20Framework/resources/app.asar/src/assets/icons/app.png net::ERR_FILE_NOT_FOUND
titlebar.css:1 
        
        
       GET file:///C:/Users/<USER>/AppData/Local/Programs/CoreDesk%20Framework/resources/app.asar/src/assets/icons/leftPanel-mask.svg net::ERR_FILE_NOT_FOUND
titlebar.css:1 
        
        
       GET file:///C:/Users/<USER>/AppData/Local/Programs/CoreDesk%20Framework/resources/app.asar/src/assets/icons/rightPanel-mask.svg net::ERR_FILE_NOT_FOUND
titlebar.css:1 
        
        
       GET file:///C:/Users/<USER>/AppData/Local/Programs/CoreDesk%20Framework/resources/app.asar/src/assets/icons/bottomPanel-mask.svg net::ERR_FILE_NOT_FOUND
activitybar.css:1 
        
        
       GET file:///C:/Users/<USER>/AppData/Local/Programs/CoreDesk%20Framework/resources/app.asar/src/assets/icons/folder-mask.svg net::ERR_FILE_NOT_FOUND
activitybar.css:1 
        
        
       GET file:///C:/Users/<USER>/AppData/Local/Programs/CoreDesk%20Framework/resources/app.asar/src/assets/icons/cloud-mask.svg net::ERR_FILE_NOT_FOUND
activitybar.css:1 
        
        
       GET file:///C:/Users/<USER>/AppData/Local/Programs/CoreDesk%20Framework/resources/app.asar/src/assets/icons/search-mask.svg net::ERR_FILE_NOT_FOUND
activitybar.css:1 
        
        
       GET file:///C:/Users/<USER>/AppData/Local/Programs/CoreDesk%20Framework/resources/app.asar/src/assets/icons/grid-mask.svg net::ERR_FILE_NOT_FOUND
activitybar.css:1 
        
        
       GET file:///C:/Users/<USER>/AppData/Local/Programs/CoreDesk%20Framework/resources/app.asar/src/assets/icons/puzzle-mask.svg net::ERR_FILE_NOT_FOUND
activitybar.css:1 
        
        
       GET file:///C:/Users/<USER>/AppData/Local/Programs/CoreDesk%20Framework/resources/app.asar/src/assets/icons/settings-mask.svg net::ERR_FILE_NOT_FOUND
GlobalLogger.js:45 [GlobalLogger] Global logger initialized successfully
constants.js:226 [Constants] Global constants loaded successfully
events.js:274 [CoreDeskEvents] Global event system initialized
Logger.js:42 [Logger] Initialized successfully
Logger.js:373 [Logger] Global logging system ready
MemoryManager.js:551 [MemoryManager] Global instance created successfully
GlobalLogger.js:9 [Memory] [MemoryManager] Initializing memory management system... 
GlobalLogger.js:9 [Memory] [MemoryManager] Memory management system initialized 
InputValidator.js:899 [InputValidator] Enhanced security validation system initialized
SecurityManager.js:841 Security [SecurityManager] Enhanced security system initialized successfully
GlobalInit.js:96 [GlobalInit] Global initialization system loaded
index.html:522 [Success] Axios loaded successfully from local file
ApiClient.js:40 [ApiClient] Initializing... baseURL: https://api.coredeskpro.com/v1
ApiClient.js:44 [ApiClient] Axios found, creating client...
ApiClient.js:57 [ApiClient] Initialization complete
AuthApiService.js:38 [AuthApiService] API client initialized successfully
IconFallback.js:18 [IconFallback] Inicializando sistema de fallback de iconos...
authConfig.js:254 authConfig [CoreDeskAuth] Global configuration loaded successfully
activityBar.js:20 UI [ActivityBar] Initializing...
activityBar.js:91 UI [ActivityBar] Icon fallbacks activated for better visibility
activityBar.js:26 UI [ActivityBar] Initialized successfully
activityBar.js:256 UI [ActivityBar] Global instance created successfully
FileExplorer.js:23 FileExplorer Initializing file explorer...
FileExplorer.js:43 FileExplorer Getting default path...
FileExplorer.js:829 FileExplorer File explorer initialized
panelManager.js:39 UI [PanelManager] Initializing...
FileExplorer.js:494 FileExplorer Loading file list for path: null
panelManager.js:472 UI [PanelManager] Opened left panel
panelManager.js:497 UI [PanelManager] Closed right panel
panelManager.js:497 UI [PanelManager] Closed bottom panel
panelManager.js:46 UI [PanelManager] Initialized successfully
panelManager.js:1208 UI [PanelManager] Global instance created successfully
SecureTokenManager.js:62 [SecureTokenManager] Encryption key initialized
FileExplorer.js:496 FileExplorer Directory listing result: {success: false, error: 'Access denied: Path not allowed'}
FileExplorer.js:543 FileExplorer Directory listing failed: {success: false, error: 'Access denied: Path not allowed'}
loadRealFileList @ FileExplorer.js:543
await in loadRealFileList (async)
generateRealFileList @ FileExplorer.js:480
generateFileList @ FileExplorer.js:468
generateExplorerContent @ FileExplorer.js:405
(anonymous) @ panelManager.js:159
updatePanelContent @ panelManager.js:808
openPanel @ panelManager.js:456
(anonymous) @ panelManager.js:862
updatePanelStates @ panelManager.js:859
initialize @ panelManager.js:44
PanelManager @ panelManager.js:32
(anonymous) @ panelManager.js:1205
statusBar.js:22 UI [StatusBar] Initializing...
statusBar.js:44 UI [StatusBar] Using existing HTML structure
statusBar.js:29 UI [StatusBar] Initialized successfully
statusBar.js:592 UI [StatusBar] Global instance created successfully
ThemeManager.js:20 ThemeManager [ThemeManager] Initializing...
ThemeManager.js:43 ThemeManager [ThemeManager] System prefers: light
ThemeManager.js:152 ThemeManager [ThemeManager] Applied theme: dark
ThemeManager.js:86 ThemeManager [ThemeManager] Loaded theme: dark
ThemeManager.js:34 ThemeManager [ThemeManager] Initialized successfully
ThemeManager.js:480 ThemeManager [ThemeManager] ThemeManager class defined and global instance created
AccountModal.js:20 UI [AccountModal] Initializing...
AccountModal.js:72 AccountModal [AccountModal] User info loaded: {name: 'Pablo Moreno', firstName: 'Pablo', lastName: 'Moreno', email: '<EMAIL>', company: '', …}
AccountModal.js:107 UI [AccountModal] User info loaded: {name: 'Pablo Moreno', firstName: 'Pablo', lastName: 'Moreno', email: '<EMAIL>', company: '', …}
AccountModal.js:639 UI [AccountModal] Class defined and global instance created successfully
AccountModal.js:32 UI [AccountModal] Initialized successfully
PanelResizer.js:24 PanelResizer Initializing panel resizer...
PanelResizer.js:32 PanelResizer Panel resizer initialized successfully
PanelResizer.js:522 PanelResizer Panel resizer component loaded successfully
UpdateDialogs.js:36 [UpdateDialogs] Initialized successfully
ConfigurationPanel.js:100 Config [ConfigurationPanel] Initializing...
ConfigurationPanel.js:128 Config [ConfigurationPanel] Loading settings...
ConfigurationPanel.js:171 Config [ConfigurationPanel] Settings loaded: {general: {…}, appearance: {…}, sync: {…}, modules: {…}, advanced: {…}, …}
ConfigurationPanel.js:2791 Config [ConfigurationPanel] Class defined and global instance created successfully
SimplifiedTabManager.js:515 SimplifiedTabManager [SimplifiedTabManager] Class defined successfully
TabContentRenderer.js:636 TabContentRenderer [TabContentRenderer] Class defined successfully
TabStateStore.js:539 TabStateStore [TabStateStore] Class defined successfully
TabEventBus.js:506 TabEventBus [TabEventBus] Class defined successfully
SimplifiedTabSystem.js:737 TabSystem [SimplifiedTabSystem] Class defined successfully
DeviceFingerprint.js:20 DeviceFingerprint [DeviceFingerprint] Initializing device fingerprint generator...
DeviceFingerprint.js:41 DeviceFingerprint [DeviceFingerprint] Collecting fingerprint components...
DeviceFingerprint.js:407 DeviceFingerprint [DeviceFingerprint] Class defined successfully
DeviceFingerprint.js:60 Component [DeviceFingerprint] Collected 23 fingerprint components
LicenseValidator.js:595 LicenseValidator [LicenseValidator] Class defined successfully
DeviceFingerprint.js:262 Component [DeviceFingerprint] Generated fingerprint: e2d6cd2d2e53788f...
DeviceFingerprint.js:26 DeviceFingerprint [DeviceFingerprint] Device fingerprint generated successfully
PanelResizer.js:399 PanelResizer Panel sizes loaded for content types: {left: 'explorer: 597.9934692382812px', right: 'default: 300px', bottom: 'terminal: 250px'}
LicenseManager.js:49 License [LicenseManager] Initializing...
LicenseValidator.js:57 LicenseValidator [LicenseValidator] Initializing...
DeviceFingerprint.js:20 DeviceFingerprint [DeviceFingerprint] Initializing device fingerprint generator...
DeviceFingerprint.js:41 DeviceFingerprint [DeviceFingerprint] Collecting fingerprint components...
DeviceFingerprint.js:20 DeviceFingerprint [DeviceFingerprint] Initializing device fingerprint generator...
DeviceFingerprint.js:41 DeviceFingerprint [DeviceFingerprint] Collecting fingerprint components...
LicenseValidator.js:57 LicenseValidator [LicenseValidator] Initializing...
DeviceFingerprint.js:20 DeviceFingerprint [DeviceFingerprint] Initializing device fingerprint generator...
DeviceFingerprint.js:41 DeviceFingerprint [DeviceFingerprint] Collecting fingerprint components...
DeviceFingerprint.js:20 DeviceFingerprint [DeviceFingerprint] Initializing device fingerprint generator...
DeviceFingerprint.js:41 DeviceFingerprint [DeviceFingerprint] Collecting fingerprint components...
LicenseManager.js:795 License [LicenseManager] Class defined and global instance created successfully
4DeviceFingerprint.js:60 Component [DeviceFingerprint] Collected 23 fingerprint components
DeviceFingerprint.js:262 Component [DeviceFingerprint] Generated fingerprint: e2d6cd2d2e53788f...
DeviceFingerprint.js:26 DeviceFingerprint [DeviceFingerprint] Device fingerprint generated successfully
DeviceFingerprint.js:262 Component [DeviceFingerprint] Generated fingerprint: e2d6cd2d2e53788f...
DeviceFingerprint.js:26 DeviceFingerprint [DeviceFingerprint] Device fingerprint generated successfully
LicenseValidator.js:64 LicenseValidator [LicenseValidator] Initialized successfully
DeviceFingerprint.js:262 Component [DeviceFingerprint] Generated fingerprint: e2d6cd2d2e53788f...
DeviceFingerprint.js:26 DeviceFingerprint [DeviceFingerprint] Device fingerprint generated successfully
DeviceFingerprint.js:262 Component [DeviceFingerprint] Generated fingerprint: e2d6cd2d2e53788f...
DeviceFingerprint.js:26 DeviceFingerprint [DeviceFingerprint] Device fingerprint generated successfully
LicenseValidator.js:64 LicenseValidator [LicenseValidator] Initialized successfully
LicenseManager.js:355 License [LicenseManager] Checking existing activation...
LicenseManager.js:361 License [LicenseManager] No stored license found
LicenseManager.js:62 License [LicenseManager] Initialized successfully
LicenseActivationModal.js:24 LicenseModal [LicenseActivationModal] Initializing...
LicenseActivationModal.js:45 LicenseModal [LicenseActivationModal] Initialized successfully
LicenseActivationModal.js:1200 LicenseModal [LicenseActivationModal] Class defined and global instance created successfully
FirebaseConnector.js:849 FirebaseConnector [FirebaseConnector] Class defined successfully
DataSyncService.js:51 Sync [DataSyncService] Initializing...
DataSyncService.js:139 Sync [DataSyncService] No license found, cloud access disabled
DataSyncService.js:1353 Sync [DataSyncService] Class defined and global instance created successfully
DataSyncService.js:116 Sync [DataSyncService] Configuration loaded {syncEnabled: false, cloudSyncEnabled: false, interval: 30000}
DataSyncService.js:78 Sync [DataSyncService] Initialized successfully
SyncStatusPanel.js:31 SyncStatus [SyncStatusPanel] Initializing...
SyncStatusPanel.js:742 SyncStatus [SyncStatusPanel] Class defined and global instance created successfully
SyncStatusPanel.js:46 SyncStatus [SyncStatusPanel] Initialized successfully
ModulePackage.js:972 ModulePackage [ModulePackage] Class loaded successfully
ModuleRegistry.js:1356 ModuleRegistry [ModuleRegistry] Class loaded successfully
ModuleDownloader.js:786 ModuleDownloader [ModuleDownloader] Service loaded successfully
DynamicStyleLoader.js:623 DynamicStyleLoader [DynamicStyleLoader] Service loaded successfully
DynamicModuleManager.js:1018 DynamicModuleManager [DynamicModuleManager] Class loaded successfully
ExclusiveModuleController.js:34 ModuleController [ExclusiveModuleController] Initializing...
DynamicModuleManager.js:71 [DynamicModuleManager] FORCE PRODUCTION: Using production module repository
DynamicModuleManager.js:47 [DynamicModuleManager] Initializing...
ModuleRegistry.js:45 [ModuleRegistry] Initializing module registry...
ExclusiveModuleController.js:931 ModuleController [ExclusiveModuleController] Global instance created successfully
UnifiedAuthManager.js:844 Auth [UnifiedAuthManager] Global instance created successfully
UnifiedAuthManager.js:57 Auth [UnifiedAuthManager] Initializing...
authConfig.js:57 authConfig [isAuthenticated] Checking authentication status...
TokenManager.js:231 TokenManager.getToken: encryptedToken exists? true
TokenManager.js:235 TokenManager.getToken: decrypted token exists? true
TokenManager.js:264 TokenManager.isTokenValid: token exists? true
TokenManager.js:265 TokenManager.isTokenValid: expiry exists? true
TokenManager.js:270 TokenManager.isTokenValid: token is valid? true expiry: Fri Jul 11 2025 00:15:44 GMT-0600 (Central Standard Time)
authConfig.js:62 authConfig [isAuthenticated] TokenManager check result: true
UnifiedAuthManager.js:685 Auth [SessionManager] Token refresh timer started
UnifiedAuthManager.js:715 Auth [SessionManager] Inactivity monitor started
UnifiedAuthManager.js:73 Auth [UnifiedAuthManager] Initialized successfully
testAuth.js:22 ⚠️ AuthTester: Using default test credentials. In Electron, environment variables are not available in renderer process.
AuthTester @ testAuth.js:22
(anonymous) @ testAuth.js:536
testAuth.js:562 🧪 Authentication Tester loaded!
testAuth.js:563 📋 Available commands:
testAuth.js:564    authTester.runAllTests() - Run complete test suite
testAuth.js:565    authTester.quickTest() - Quick authentication test
testAuth.js:566    authTester.testCredentials(email, password) - Test specific credentials
testAuth.js:567    authTester.testLogout() - Test logout functionality
testAuth.js:568    authTester.testTokenEncryption() - Test token encryption/decryption
testAuth.js:569    authTester.debugStorage() - Show localStorage contents
testAuth.js:570    authTester.clearAuthData() - Clear all auth data
debugTokenPersistence.js:537 🔧 Token Persistence Debugger loaded!
debugTokenPersistence.js:538 📋 Available commands:
debugTokenPersistence.js:539    tokenPersistenceDebugger.runPersistenceTest() - Full persistence test
debugTokenPersistence.js:540    tokenPersistenceDebugger.showCurrentState() - Show current auth state
debugTokenPersistence.js:541    tokenPersistenceDebugger.testLogout() - Test logout functionality specifically
debugTokenPersistence.js:542    tokenPersistenceDebugger.testComprehensiveLogout() - Test the COMPREHENSIVE logout fix
debugTokenPersistence.js:543    tokenPersistenceDebugger.testEnhancedLogout() - Test the ENHANCED logout fix with persistent protection
debugTokenPersistence.js:544    tokenPersistenceDebugger.forceCleanup() - Force clear all auth data (manual cleanup)
FileExplorer.js:51 FileExplorer Attempting to get CoreDesk path...
FileExplorer.js:53 FileExplorer CoreDesk path result: {success: true, path: 'C:\\Users\\<USER>\\OneDrive\\Documents\\CoreDesk'}
FileExplorer.js:55 FileExplorer Using CoreDesk path: C:\Users\<USER>\OneDrive\Documents\CoreDesk
index.html:96 ✅ IMMEDIATE AUTH GATE: Added authenticated class to body
IconFallback.js:34 [IconFallback] Verificando carga de iconos SVG...
IconFallback.js:38 [IconFallback] Soporte mask-image: true
IconFallback.js:41 [IconFallback] Activando fallback para evitar errores de carga SVG
IconFallback.js:108 [IconFallback] Activando sistema de fallback...
IconFallback.js:196 [IconFallback] Estilos de fallback agregados
IconFallback.js:199 [IconFallback] Clase icon-fallback-active aplicada: true
IconFallback.js:203 [IconFallback] Iconos de panel encontrados: 4
IconFallback.js:205 [IconFallback] Panel icon 0: panel-icon icon-layout-sidebar-left rgb(150, 150, 150)
IconFallback.js:205 [IconFallback] Panel icon 1: panel-icon icon-layout-sidebar-right rgb(150, 150, 150)
IconFallback.js:205 [IconFallback] Panel icon 2: panel-icon icon-layout-panel rgb(150, 150, 150)
IconFallback.js:205 [IconFallback] Panel icon 3: SVGAnimatedString {baseVal: 'panel-icon', animVal: 'panel-icon'} rgb(150, 150, 150)
IconFallback.js:117 [IconFallback] Sistema de fallback activado
authConfig.js:236 authConfig [CoreDeskAuth] Configuration initialized
authConfig.js:241 authConfig [CoreDeskAuth] Deferring authentication to AuthGuard - preventing race conditions
titleBar.js:15 UI [TitleBar] Initializing...
titleBar.js:19 UI [TitleBar] Maximize button found: true
titleBar.js:34 UI [TitleBar] Account button found, adding click listener
titleBar.js:55 UI [TitleBar] Dropdown state initialized as hidden
titleBar.js:61 UI [TitleBar] My Account option found, adding click listener
titleBar.js:73 UI [TitleBar] Logout option found, adding click listener
titleBar.js:24 UI [TitleBar] Initialized successfully
titleBar.js:388 UI [TitleBar] Global instance created successfully
app.js:16 [CoreDeskApp] Could not set global app reference:
warn @ app.js:16
CoreDeskApp @ app.js:42
(anonymous) @ app.js:2083
app.js:14 [CoreDeskApp] Starting application initialization...
app.js:60 CoreDeskApp ✅ Starting dashboard initialization (auth verified synchronously)
app.js:61 *** COREDESK APP.JS - FORCED PRODUCTION URL VERSION ***
app.js:62 *** THIS MESSAGE CONFIRMS THE NEW CODE IS RUNNING ***
app.js:67 CoreDeskApp Initializing AuthGuard as central auth authority
AuthGuard.js:19 Auth [AuthGuard] Initializing authentication guard...
AuthGuard.js:25 Auth [AuthGuard] Performing initial authentication check
AuthGuard.js:54 Auth [AuthGuard] Starting authentication check for path: /C:/Users/<USER>/AppData/Local/Programs/CoreDesk%20Framework/resources/app.asar/src/index.html
authConfig.js:57 authConfig [isAuthenticated] Checking authentication status...
TokenManager.js:231 TokenManager.getToken: encryptedToken exists? true
TokenManager.js:235 TokenManager.getToken: decrypted token exists? true
TokenManager.js:264 TokenManager.isTokenValid: token exists? true
TokenManager.js:265 TokenManager.isTokenValid: expiry exists? true
TokenManager.js:270 TokenManager.isTokenValid: token is valid? true expiry: Fri Jul 11 2025 00:15:44 GMT-0600 (Central Standard Time)
authConfig.js:62 authConfig [isAuthenticated] TokenManager check result: true
TokenManager.js:231 TokenManager.getToken: encryptedToken exists? true
TokenManager.js:235 TokenManager.getToken: decrypted token exists? true
TokenManager.js:264 TokenManager.isTokenValid: token exists? true
TokenManager.js:265 TokenManager.isTokenValid: expiry exists? true
TokenManager.js:270 TokenManager.isTokenValid: token is valid? true expiry: Fri Jul 11 2025 00:15:44 GMT-0600 (Central Standard Time)
AuthGuard.js:104 Auth [AuthGuard] User authenticated, access granted
AuthGuard.js:35 Auth [AuthGuard] Authentication guard initialized successfully
app.js:14 [CoreDeskApp] Setting up electronAPI listeners
app.js:275 [CoreDeskApp] Title bar initialized
app.js:14 [CoreDeskApp] Account dropdown initialized
ModuleRegistry.js:45 [ModuleRegistry] Loading installed modules from database...
ModuleRegistry.js:45 [ModulePersistenceClient] Starting initialization...
ModuleRegistry.js:45 [ModulePersistenceClient] ElectronAPI database interface verified
ModuleRegistry.js:45 [ModulePersistenceClient] Testing database connection...
FileExplorer.js:31 FileExplorer File explorer initialized with path: C:\Users\<USER>\OneDrive\Documents\CoreDesk
FileExplorer.js:494 FileExplorer Loading file list for path: C:\Users\<USER>\OneDrive\Documents\CoreDesk
FileExplorer.js:494 FileExplorer Loading file list for path: C:\Users\<USER>\OneDrive\Documents\CoreDesk
ModuleRegistry.js:46 [ModulePersistenceClient] Database connection test failed: Error: Database query failed: Database not initialized
    at ModulePersistenceClient.testConnection (ModulePersistenceClient.js:83:23)
    at async ModulePersistenceClient.initialize (ModulePersistenceClient.js:42:13)
    at async ModuleRegistry.loadInstalledModulesFromStorage (ModuleRegistry.js:529:21)
    at async ModuleRegistry.loadPersistedData (ModuleRegistry.js:510:13)
    at async ModuleRegistry.initialize (ModuleRegistry.js:64:17)
    at async DynamicModuleManager.createDefaultRegistry (DynamicModuleManager.js:886:13)
    at async DynamicModuleManager.initialize (DynamicModuleManager.js:85:54)
    at async ExclusiveModuleController.initializeDynamicSystem (ExclusiveModuleController.js:75:13)
    at async ExclusiveModuleController.initialize (ExclusiveModuleController.js:37:9)
error @ ModuleRegistry.js:46
testConnection @ ModulePersistenceClient.js:92
await in testConnection (async)
initialize @ ModulePersistenceClient.js:42
loadInstalledModulesFromStorage @ ModuleRegistry.js:529
loadPersistedData @ ModuleRegistry.js:510
initialize @ ModuleRegistry.js:64
await in initialize (async)
createDefaultRegistry @ DynamicModuleManager.js:886
initialize @ DynamicModuleManager.js:85
initializeDynamicSystem @ ExclusiveModuleController.js:75
initialize @ ExclusiveModuleController.js:37
ExclusiveModuleController @ ExclusiveModuleController.js:27
(anonymous) @ ExclusiveModuleController.js:929
ModuleRegistry.js:46 [ModulePersistenceClient] Failed to initialize: Error: Database query failed: Database not initialized
    at ModulePersistenceClient.testConnection (ModulePersistenceClient.js:83:23)
    at async ModulePersistenceClient.initialize (ModulePersistenceClient.js:42:13)
    at async ModuleRegistry.loadInstalledModulesFromStorage (ModuleRegistry.js:529:21)
    at async ModuleRegistry.loadPersistedData (ModuleRegistry.js:510:13)
    at async ModuleRegistry.initialize (ModuleRegistry.js:64:17)
    at async DynamicModuleManager.createDefaultRegistry (DynamicModuleManager.js:886:13)
    at async DynamicModuleManager.initialize (DynamicModuleManager.js:85:54)
    at async ExclusiveModuleController.initializeDynamicSystem (ExclusiveModuleController.js:75:13)
    at async ExclusiveModuleController.initialize (ExclusiveModuleController.js:37:9)
error @ ModuleRegistry.js:46
initialize @ ModulePersistenceClient.js:55
await in initialize (async)
loadInstalledModulesFromStorage @ ModuleRegistry.js:529
loadPersistedData @ ModuleRegistry.js:510
initialize @ ModuleRegistry.js:64
await in initialize (async)
createDefaultRegistry @ DynamicModuleManager.js:886
initialize @ DynamicModuleManager.js:85
initializeDynamicSystem @ ExclusiveModuleController.js:75
initialize @ ExclusiveModuleController.js:37
ExclusiveModuleController @ ExclusiveModuleController.js:27
(anonymous) @ ExclusiveModuleController.js:929
ModuleRegistry.js:46 [ModulePersistenceClient] Failed to get installed modules: Error: Client not initialized
    at ModulePersistenceClient.getInstalledModules (ModulePersistenceClient.js:208:23)
    at ModulePersistenceClient.getActiveModules (ModulePersistenceClient.js:351:43)
    at ModuleRegistry.loadInstalledModulesFromStorage (ModuleRegistry.js:538:75)
    at async ModuleRegistry.loadPersistedData (ModuleRegistry.js:510:13)
    at async ModuleRegistry.initialize (ModuleRegistry.js:64:17)
    at async DynamicModuleManager.createDefaultRegistry (DynamicModuleManager.js:886:13)
    at async DynamicModuleManager.initialize (DynamicModuleManager.js:85:54)
    at async ExclusiveModuleController.initializeDynamicSystem (ExclusiveModuleController.js:75:13)
    at async ExclusiveModuleController.initialize (ExclusiveModuleController.js:37:9)
error @ ModuleRegistry.js:46
getInstalledModules @ ModulePersistenceClient.js:236
getActiveModules @ ModulePersistenceClient.js:351
loadInstalledModulesFromStorage @ ModuleRegistry.js:538
await in loadInstalledModulesFromStorage (async)
loadPersistedData @ ModuleRegistry.js:510
initialize @ ModuleRegistry.js:64
await in initialize (async)
createDefaultRegistry @ DynamicModuleManager.js:886
initialize @ DynamicModuleManager.js:85
initializeDynamicSystem @ ExclusiveModuleController.js:75
initialize @ ExclusiveModuleController.js:37
ExclusiveModuleController @ ExclusiveModuleController.js:27
(anonymous) @ ExclusiveModuleController.js:929
ModuleRegistry.js:45 [ModuleRegistry] Found 0 active modules in database
authConfig.js:250 authConfig [CoreDeskAuth] Dependencies initialized - AuthGuard will handle authentication
FileExplorer.js:496 FileExplorer Directory listing result: {success: true, path: 'C:\\Users\\<USER>\\OneDrive\\Documents\\CoreDesk', files: Array(1), directories: Array(5), total: 6}
FileExplorer.js:538 FileExplorer Processed files for rendering: (7) [{…}, {…}, {…}, {…}, {…}, {…}, {…}]
FileExplorer.js:496 FileExplorer Directory listing result: {success: true, path: 'C:\\Users\\<USER>\\OneDrive\\Documents\\CoreDesk', files: Array(1), directories: Array(5), total: 6}
FileExplorer.js:538 FileExplorer Processed files for rendering: (7) [{…}, {…}, {…}, {…}, {…}, {…}, {…}]
GlobalInit.js:12 [GlobalInit] Initializing CoreDesk components...
GlobalInit.js:17 [GlobalInit] InputValidator dependencies initialized
SecurityManager.js:72 Security [SecurityManager] Initializing security policies...
SecurityManager.js:126 Security [SecurityManager] CSP configured successfully
SecurityManager.js:78 Security [SecurityManager] Security manager initialized successfully
GlobalInit.js:23 [GlobalInit] SecurityManager dependencies initialized
AuthApiService.js:38 [AuthApiService] API client initialized successfully
GlobalInit.js:29 [GlobalInit] AuthApiService dependencies initialized
GlobalInit.js:34 [GlobalInit] LicenseApiService dependencies initialized
GlobalInit.js:40 [GlobalInit] UnifiedAuthManager dependencies initialized
GlobalInit.js:53 [GlobalInit] All CoreDesk components initialized successfully
IconFallback.js:221 [IconFallback] Forzando visibilidad de iconos de paneles...
IconFallback.js:252 [IconFallback] Iconos de paneles forzados a ser visibles
ModuleRegistry.js:45 [ModuleRegistry] Loaded 0 installed modules from database
ModuleRegistry.js:45 [ModuleRegistry] Registry initialized with 0 modules
DynamicModuleManager.js:47 [DynamicModuleManager] Creating ModuleDownloader instance
DynamicModuleManager.js:71 [DynamicModuleManager] FORCE PRODUCTION: Using production module repository
ModuleDownloader.js:66 [ModuleDownloader] FORCE PRODUCTION: Using production module repository
ModuleDownloader.js:49 [ModuleDownloader] Downloader initialized
DynamicModuleManager.js:47 [DynamicModuleManager] Creating ModulePersistenceClient instance
ModuleRegistry.js:45 [ModuleRegistry] Initializing module registry...
ModuleRegistry.js:45 [ModuleRegistry] Loading installed modules from database...
ModuleRegistry.js:46 [ModulePersistenceClient] Failed to get installed modules: Error: Client not initialized
    at ModulePersistenceClient.getInstalledModules (ModulePersistenceClient.js:208:23)
    at ModulePersistenceClient.getActiveModules (ModulePersistenceClient.js:351:43)
    at ModuleRegistry.loadInstalledModulesFromStorage (ModuleRegistry.js:538:75)
    at ModuleRegistry.loadPersistedData (ModuleRegistry.js:510:24)
    at ModuleRegistry.initialize (ModuleRegistry.js:64:28)
    at async DynamicModuleManager.initialize (DynamicModuleManager.js:92:13)
    at async ExclusiveModuleController.initializeDynamicSystem (ExclusiveModuleController.js:75:13)
    at async ExclusiveModuleController.initialize (ExclusiveModuleController.js:37:9)
error @ ModuleRegistry.js:46
getInstalledModules @ ModulePersistenceClient.js:236
getActiveModules @ ModulePersistenceClient.js:351
loadInstalledModulesFromStorage @ ModuleRegistry.js:538
loadPersistedData @ ModuleRegistry.js:510
initialize @ ModuleRegistry.js:64
await in initialize (async)
initialize @ DynamicModuleManager.js:92
await in initialize (async)
initializeDynamicSystem @ ExclusiveModuleController.js:75
initialize @ ExclusiveModuleController.js:37
ExclusiveModuleController @ ExclusiveModuleController.js:27
(anonymous) @ ExclusiveModuleController.js:929
ModuleRegistry.js:45 [ModuleRegistry] Found 0 active modules in database
ModuleRegistry.js:45 [ModuleRegistry] Loaded 0 installed modules from database
ModuleRegistry.js:45 [ModuleRegistry] Registry initialized with 0 modules
DynamicModuleManager.js:47 [ModulePersistenceClient] Starting initialization...
DynamicModuleManager.js:47 [ModulePersistenceClient] ElectronAPI database interface verified
DynamicModuleManager.js:47 [ModulePersistenceClient] Testing database connection...
DynamicModuleManager.js:48 [ModulePersistenceClient] Database connection test failed: Error: Database query failed: Database not initialized
    at ModulePersistenceClient.testConnection (ModulePersistenceClient.js:83:23)
    at async ModulePersistenceClient.initialize (ModulePersistenceClient.js:42:13)
    at async DynamicModuleManager.initialize (DynamicModuleManager.js:97:17)
    at async ExclusiveModuleController.initializeDynamicSystem (ExclusiveModuleController.js:75:13)
    at async ExclusiveModuleController.initialize (ExclusiveModuleController.js:37:9)
error @ DynamicModuleManager.js:48
testConnection @ ModulePersistenceClient.js:92
await in testConnection (async)
initialize @ ModulePersistenceClient.js:42
initialize @ DynamicModuleManager.js:97
await in initialize (async)
initializeDynamicSystem @ ExclusiveModuleController.js:75
initialize @ ExclusiveModuleController.js:37
ExclusiveModuleController @ ExclusiveModuleController.js:27
(anonymous) @ ExclusiveModuleController.js:929
DynamicModuleManager.js:48 [ModulePersistenceClient] Failed to initialize: Error: Database query failed: Database not initialized
    at ModulePersistenceClient.testConnection (ModulePersistenceClient.js:83:23)
    at async ModulePersistenceClient.initialize (ModulePersistenceClient.js:42:13)
    at async DynamicModuleManager.initialize (DynamicModuleManager.js:97:17)
    at async ExclusiveModuleController.initializeDynamicSystem (ExclusiveModuleController.js:75:13)
    at async ExclusiveModuleController.initialize (ExclusiveModuleController.js:37:9)
error @ DynamicModuleManager.js:48
initialize @ ModulePersistenceClient.js:55
await in initialize (async)
initialize @ DynamicModuleManager.js:97
await in initialize (async)
initializeDynamicSystem @ ExclusiveModuleController.js:75
initialize @ ExclusiveModuleController.js:37
ExclusiveModuleController @ ExclusiveModuleController.js:27
(anonymous) @ ExclusiveModuleController.js:929
DynamicModuleManager.js:47 [DynamicModuleManager] Module persistence service initialized
DynamicModuleManager.js:47 [DynamicModuleManager] Loaded 0 installed modules
DynamicModuleManager.js:47 [DynamicModuleManager] Initialized successfully
ExclusiveModuleController.js:80 ModuleController [ExclusiveModuleController] Dynamic module system initialized
DynamicModuleManager.js:47 [DynamicModuleManager] Refreshing registry...
ModuleRegistry.js:45 [ModuleRegistry] Initializing module registry...
ModuleRegistry.js:45 [ModuleRegistry] Loading installed modules from database...
ModuleRegistry.js:46 [ModulePersistenceClient] Failed to get installed modules: Error: Client not initialized
    at ModulePersistenceClient.getInstalledModules (ModulePersistenceClient.js:208:23)
    at ModulePersistenceClient.getActiveModules (ModulePersistenceClient.js:351:43)
    at ModuleRegistry.loadInstalledModulesFromStorage (ModuleRegistry.js:538:75)
    at ModuleRegistry.loadPersistedData (ModuleRegistry.js:510:24)
    at ModuleRegistry.initialize (ModuleRegistry.js:64:28)
    at async DynamicModuleManager.refreshRegistry (DynamicModuleManager.js:724:13)
    at async ExclusiveModuleController.initializeAvailableModules (ExclusiveModuleController.js:94:9)
    at async ExclusiveModuleController.initialize (ExclusiveModuleController.js:40:9)
error @ ModuleRegistry.js:46
getInstalledModules @ ModulePersistenceClient.js:236
getActiveModules @ ModulePersistenceClient.js:351
loadInstalledModulesFromStorage @ ModuleRegistry.js:538
loadPersistedData @ ModuleRegistry.js:510
initialize @ ModuleRegistry.js:64
await in initialize (async)
refreshRegistry @ DynamicModuleManager.js:724
initializeAvailableModules @ ExclusiveModuleController.js:94
initialize @ ExclusiveModuleController.js:40
await in initialize (async)
ExclusiveModuleController @ ExclusiveModuleController.js:27
(anonymous) @ ExclusiveModuleController.js:929
ModuleRegistry.js:45 [ModuleRegistry] Found 0 active modules in database
ModuleRegistry.js:45 [ModuleRegistry] Loaded 0 installed modules from database
ModuleRegistry.js:45 [ModuleRegistry] Registry initialized with 0 modules
DynamicModuleManager.js:47 [DynamicModuleManager] Loaded 0 installed modules
DynamicModuleManager.js:47 [DynamicModuleManager] Registry refreshed successfully
ExclusiveModuleController.js:98 ModuleController [ExclusiveModuleController] Loaded 0 dynamic modules
ExclusiveModuleController.js:102 ModuleController [ExclusiveModuleController] No modules found, checking filesystem...
ModuleRegistry.js:45 [ModuleRegistry] Loading installed modules from database...
ModuleRegistry.js:46 [ModulePersistenceClient] Failed to get installed modules: Error: Client not initialized
    at ModulePersistenceClient.getInstalledModules (ModulePersistenceClient.js:208:23)
    at ModulePersistenceClient.getActiveModules (ModulePersistenceClient.js:351:43)
    at ModuleRegistry.loadInstalledModulesFromStorage (ModuleRegistry.js:538:75)
    at ExclusiveModuleController.loadModulesFromFilesystem (ExclusiveModuleController.js:115:32)
    at ExclusiveModuleController.initializeAvailableModules (ExclusiveModuleController.js:103:24)
    at async ExclusiveModuleController.initialize (ExclusiveModuleController.js:40:9)
error @ ModuleRegistry.js:46
getInstalledModules @ ModulePersistenceClient.js:236
getActiveModules @ ModulePersistenceClient.js:351
loadInstalledModulesFromStorage @ ModuleRegistry.js:538
loadModulesFromFilesystem @ ExclusiveModuleController.js:115
initializeAvailableModules @ ExclusiveModuleController.js:103
await in initializeAvailableModules (async)
initialize @ ExclusiveModuleController.js:40
await in initialize (async)
ExclusiveModuleController @ ExclusiveModuleController.js:27
(anonymous) @ ExclusiveModuleController.js:929
ModuleRegistry.js:45 [ModuleRegistry] Found 0 active modules in database
ModuleRegistry.js:45 [ModuleRegistry] Loaded 0 installed modules from database
DynamicModuleManager.js:47 [DynamicModuleManager] Loaded 0 installed modules
ExclusiveModuleController.js:122 ModuleController [ExclusiveModuleController] Loaded 0 modules from filesystem
ExclusiveModuleController.js:51 ModuleController [ExclusiveModuleController] Initialized successfully
ExclusiveModuleController.js:52 ModuleController [ExclusiveModuleController] Using dynamic module system
ExclusiveModuleController.js:57 ModuleController [ExclusiveModuleController] No active module detected
ConfigurationPanel.js:115 Config [ConfigurationPanel] Initialized successfully
app.js:16 [CoreDeskApp] Backend not available - continuing in frontend-only mode
warn @ app.js:16
(anonymous) @ app.js:171
(anonymous) @ MemoryManager.js:120
setTimeout (async)
window.setTimeout @ MemoryManager.js:118
waitForBackendOrTimeout @ app.js:170
initializeComponents @ app.js:131
initialize @ app.js:84
CoreDeskApp @ app.js:48
(anonymous) @ app.js:2083
app.js:14 [CoreDeskApp] Application initialized successfully
app.js:620 [CoreDeskApp] *** loadDynamicModulesGrid() CALLED ***
app.js:621 [CoreDeskApp] *** ASYNC FUNCTION FIXED ***
app.js:14 [CoreDeskApp] Loading dynamic modules grid (attempt 1)
app.js:14 [CoreDeskApp] Starting module loading process...
app.js:14 [CoreDeskApp] Dynamic manager available: true, Registry available: true
app.js:14 [CoreDeskApp] Found 0 installed modules, loading server modules as well
app.js:811 [CoreDeskApp] *** showServerModules() CALLED ***
app.js:812 [CoreDeskApp] *** STARTING SERVER MODULE LOAD ***
app.js:14 [CoreDeskApp] Loading modules from server (clean architecture)
app.js:1030 [CoreDeskApp] FORCE PRODUCTION: Using production module repository
app.js:14 [CoreDeskApp] Fetching modules from URL: https://coredeskpro.com/api/modules
app.js:829 [CoreDeskApp] Debug: Attempting to fetch modules from: https://coredeskpro.com/api/modules
app.js:830 [CoreDeskApp] Debug: User agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) coredesk/0.0.2 Chrome/126.0.6478.234 Electron/31.7.7 Safari/537.36
app.js:831 [CoreDeskApp] Debug: Location hostname: 
app.js:832 [CoreDeskApp] Debug: *** FORCING PRODUCTION URLS ***
app.js:837 [CoreDeskApp] Debug: Cache-busted URL: https://coredeskpro.com/api/modules?cb=1752130127415
app.js:846 [CoreDeskApp] Debug: Response status: 200
app.js:853 [CoreDeskApp] Debug: API response: {success: true, modules: Array(4)}
app.js:14 [CoreDeskApp] Loaded 4 modules from server
app.js:861 [CoreDeskApp] Debug: Modules loaded: (4) [{…}, {…}, {…}, {…}]
app.js:862 [CoreDeskApp] Debug: *** SUCCESS - MODULES LOADED FROM PRODUCTION ***
app.js:879 [CoreDeskApp] *** CALLING displayServerModules ***
app.js:898 [CoreDeskApp] *** displayServerModules() CALLED ***
app.js:899 [CoreDeskApp] *** MODULES TO DISPLAY: (4) [{…}, {…}, {…}, {…}]
app.js:905 [CoreDeskApp] Module lexflow: downloadable=true, installed=false
app.js:905 [CoreDeskApp] Module protocolx: downloadable=true, installed=false
app.js:905 [CoreDeskApp] Module auditpro: downloadable=true, installed=false
app.js:905 [CoreDeskApp] Module finsync: downloadable=true, installed=false
app.js:969 [CoreDeskApp] *** MODULE GRID UPDATED WITH PRODUCTION DATA ***
app.js:970 [CoreDeskApp] *** THIRD-PARTY BUTTON SHOULD BE VISIBLE ***
app.js:881 [CoreDeskApp] *** displayServerModules COMPLETED ***
app.js:14 [CoreDeskApp] Setting up module dropzone
app.js:14 [CoreDeskApp] Module dropzone setup completed
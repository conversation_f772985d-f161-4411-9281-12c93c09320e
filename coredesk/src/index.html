<!DOCTYPE html>
<html lang="en" data-theme="dark" class="theme-dark">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self' https://api.coredeskpro.com https://*.coredeskpro.com https://portal.coredeskpro.com https://coredeskpro.com http://coredeskpro.com http://*.coredeskpro.com http://localhost:* https://localhost:*;">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>CoreDesk Framework v0.0.2</title>
    
    <!-- CRITICAL: Hide everything until authentication is verified -->
    <style>
        /* Hide entire page content until auth check completes */
        body {
            visibility: hidden !important;
            opacity: 0 !important;
        }
        
        /* Show loading message during auth check */
        .auth-loading {
            visibility: visible !important;
            opacity: 1 !important;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #1e1e1e;
            color: #ffffff;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            z-index: 999999;
        }
        
        /* Hide loading message when authenticated */
        body.authenticated .auth-loading {
            display: none !important;
        }
        
        /* Show content only when authenticated */
        body.authenticated {
            visibility: visible !important;
            opacity: 1 !important;
        }
    </style>
    
    <!-- IMMEDIATE Authentication Check - BEFORE any content renders -->
    <script>
        // This runs in <head> BEFORE body content is parsed
        console.log('🔒 IMMEDIATE AUTH GATE: Checking BEFORE any content renders...');
        
        try {
            // Check localStorage immediately - no dependencies needed
            const hasToken = !!localStorage.getItem('coredesk_token');
            const hasExpiry = !!localStorage.getItem('coredesk_token_expiry');
            
            console.log('🔍 IMMEDIATE AUTH GATE: Token in localStorage:', hasToken);
            console.log('🔍 IMMEDIATE AUTH GATE: Expiry in localStorage:', hasExpiry);
            
            // Quick token validity check
            let tokenValid = false;
            if (hasToken && hasExpiry) {
                const expiry = localStorage.getItem('coredesk_token_expiry');
                const now = Date.now();
                
                let expiryTime;
                if (expiry.includes('T') || expiry.includes('-')) {
                    expiryTime = new Date(expiry).getTime();
                } else {
                    expiryTime = parseInt(expiry);
                }
                
                tokenValid = !isNaN(expiryTime) && now < expiryTime;
                console.log('🔍 IMMEDIATE AUTH GATE: Token is valid:', tokenValid);
            }
            
            // Immediate redirect if not authenticated - BEFORE content appears
            if (!hasToken || !tokenValid) {
                console.log('❌ IMMEDIATE AUTH GATE: Not authenticated - redirecting BEFORE content renders');
                
                // Prevent any content from showing
                document.documentElement.style.display = 'none';
                
                // Immediate redirect
                window.location.replace('login.html');
                
                // Stop execution
                throw new Error('AUTH_REDIRECT_BEFORE_RENDER');
            } else {
                console.log('✅ IMMEDIATE AUTH GATE: Authenticated - allowing content to render');
                
                // Set flag for later scripts
                window._authVerifiedInHead = true;
                
                // Allow content to show when DOM is ready
                document.addEventListener('DOMContentLoaded', () => {
                    document.body.classList.add('authenticated');
                    console.log('✅ IMMEDIATE AUTH GATE: Added authenticated class to body');
                });
                
                // Fallback timeout to prevent hanging
                setTimeout(() => {
                    if (!document.body.classList.contains('authenticated')) {
                        console.warn('⚠️ IMMEDIATE AUTH GATE: Authentication timeout - redirecting to login');
                        window.location.replace('login.html');
                    }
                }, 5000);
            }
            
        } catch (error) {
            if (error.message !== 'AUTH_REDIRECT_BEFORE_RENDER') {
                console.error('❌ IMMEDIATE AUTH GATE: Error:', error);
                document.documentElement.style.display = 'none';
                window.location.replace('login.html');
            }
        }
    </script>
    
    <!-- Base CSS -->
    <link rel="stylesheet" href="css/base/reset.css">
    <link rel="stylesheet" href="css/base/variables.css">
    <link rel="stylesheet" href="css/base/typography.css">
    
    <!-- Component CSS -->
    <link rel="stylesheet" href="css/components/titlebar.css">
    <link rel="stylesheet" href="css/components/activitybar.css">
    <link rel="stylesheet" href="css/components/panels.css">
    <link rel="stylesheet" href="css/components/panels-content.css">
    <link rel="stylesheet" href="css/components/tabs.css">
    <link rel="stylesheet" href="css/components/statusbar.css">
    <link rel="stylesheet" href="css/components/modals.css">
    <link rel="stylesheet" href="css/components/theme-toggle.css">
    <link rel="stylesheet" href="css/components/account-modal.css">
    <link rel="stylesheet" href="css/components/config-panel.css">
    <link rel="stylesheet" href="css/components/file-explorer.css">
    <link rel="stylesheet" href="css/components/dashboard.css">
    <link rel="stylesheet" href="css/components/search-panel.css">
    <link rel="stylesheet" href="css/components/cloud-panel.css">
    <link rel="stylesheet" href="css/components/notification-center.css">
    <link rel="stylesheet" href="css/components/update-dialogs.css">
    <link rel="stylesheet" href="css/license-activation.css">
    <link rel="stylesheet" href="css/sync-status.css">
    
    <!-- Module CSS will be loaded dynamically by DynamicStyleLoader -->
    
    <!-- Theme CSS -->
    <link rel="stylesheet" href="css/themes/dark.css">
    <link rel="stylesheet" href="css/themes/light.css">
    
    <!-- Main layout CSS -->
    <link rel="stylesheet" href="css/base/layout.css">
</head>
<body>
    <!-- Authentication loading message (visible while page loads for authenticated users) -->
    <div class="auth-loading">
        🔒 Verifying authentication...
    </div>
    
    <!-- Main application container -->
    <div id="app" class="app-container">
        
        <!-- Title Bar (VS Code style) -->
        <div id="titlebar" class="titlebar">
            <div class="titlebar-left">
                <div class="app-logo">
                    <img src="assets/icons/app.png" alt="CoreDesk" class="app-icon">
                    <span class="app-name">CoreDesk</span>
                    <span class="app-version">v0.0.2</span>
                </div>
            </div>
            
            <div class="titlebar-center">
                <!-- Country Indicator -->
                <div class="country-indicator">
                    <span id="country-flag" class="flag-icon">🇺🇸</span>
                    <span id="country-name">United States</span>
                </div>
            </div>
            
            <div class="titlebar-right">
                <!-- Panel toggles -->
                <div class="panel-toggles">
                    <button id="toggle-left-panel" class="panel-toggle" title="Toggle Left Panel">
                        <div class="panel-icon icon-layout-sidebar-left"></div>
                    </button>
                    <button id="toggle-right-panel" class="panel-toggle" title="Toggle Right Panel">
                        <div class="panel-icon icon-layout-sidebar-right"></div>
                    </button>
                    <button id="toggle-bottom-panel" class="panel-toggle" title="Toggle Bottom Panel">
                        <div class="panel-icon icon-layout-panel"></div>
                    </button>
                </div>
                
                <!-- Notifications -->
                <div class="notifications-menu">
                    <button id="notifications-button" class="notifications-button" title="Notificaciones">
                        <span class="notifications-icon">🔔</span>
                        <span class="notification-badge" style="display: none;">0</span>
                    </button>
                </div>
                
                <!-- Account menu -->
                <div class="account-menu">
                    <button id="account-button" class="account-button" title="Account">
                        <span class="account-text">Mi Cuenta</span>
                        <span class="account-icon">👤</span>
                        <span class="dropdown-arrow">▼</span>
                    </button>
                    
                    <!-- Account dropdown menu -->
                    <div id="account-dropdown" class="account-dropdown" style="display: none;">
                        <div class="dropdown-item" id="my-account-option">
                            <span class="dropdown-icon">👤</span>
                            <span class="dropdown-text">Mi Cuenta</span>
                        </div>
                        <div class="dropdown-separator"></div>
                        <div class="dropdown-item" id="logout-option">
                            <span class="dropdown-icon">🚪</span>
                            <span class="dropdown-text">Cerrar Sesión</span>
                        </div>
                    </div>
                </div>
                
                <!-- Window controls -->
                <div class="window-controls">
                    <button id="minimize-btn" class="window-control" title="Minimize">
                        <div class="control-icon icon-minimize"></div>
                    </button>
                    <button id="maximize-btn" class="window-control" title="Maximize">
                        <div class="control-icon icon-maximize"></div>
                    </button>
                    <button id="close-btn" class="window-control close" title="Close">
                        <div class="control-icon icon-close"></div>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Main content area -->
        <div class="main-content">
            
            <!-- Activity Bar (VS Code style) -->
            <div id="activitybar" class="activity-bar">
                <div class="activity-bar-items">
                    <button id="explorer-button" class="activity-button active" title="Explorer" data-panel="explorer">
                        <div class="activity-icon icon-explorer"></div>
                    </button>
                    <button id="cloud-button" class="activity-button" title="Cloud" data-panel="cloud">
                        <div class="activity-icon icon-cloud"></div>
                    </button>
                    <button id="search-button" class="activity-button" title="Search" data-panel="search">
                        <div class="activity-icon icon-search"></div>
                    </button>
                    <button id="modules-button" class="activity-button" title="Modules" data-panel="modules">
                        <div class="activity-icon icon-modules"></div>
                    </button>
                    <button id="extensions-button" class="activity-button" title="Extensions" data-panel="extensions">
                        <div class="activity-icon icon-extensions"></div>
                    </button>
                </div>
                
                <div class="activity-bar-bottom">
                    <button id="settings-button" class="activity-button" title="Settings">
                        <div class="activity-icon icon-settings"></div>
                    </button>
                </div>
            </div>
            
            <!-- Left Panel -->
            <div id="left-panel" class="side-panel left-panel">
                <div class="panel-header">
                    <h3 id="left-panel-title">Explorer</h3>
                    <button class="panel-close" data-panel="left">×</button>
                </div>
                <div id="left-panel-content" class="panel-content">
                    <!-- Dynamic content based on active activity button -->
                </div>
            </div>
            
            <!-- Editor area -->
            <div class="editor-container">
                <!-- Tab bar -->
                <div id="tab-bar" class="tab-bar">
                    <div id="tab-list" class="tab-list">
                        <!-- Tabs will be dynamically added here -->
                    </div>
                    <div class="tab-controls">
                        <button id="new-tab-button" class="new-tab-button" title="New Tab">+</button>
                    </div>
                </div>
                
                <!-- Tab content area -->
                <div id="tab-content" class="tab-content">
                    <!-- Default welcome screen -->
                    <div id="welcome-screen" class="welcome-screen">
                        <!-- User Dashboard -->
                        <div class="dashboard-header">
                            <div class="user-greeting">
                                <h1 id="user-greeting-text">Bienvenido a CoreDesk Framework</h1>
                                <p id="user-greeting-subtitle">Selecciona un módulo para comenzar</p>
                            </div>
                            <div class="user-stats">
                                <div class="stat-card">
                                    <div class="stat-icon">📊</div>
                                    <div class="stat-info">
                                        <span class="stat-value" id="stat-projects">0</span>
                                        <span class="stat-label">Proyectos</span>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">📄</div>
                                    <div class="stat-info">
                                        <span class="stat-value" id="stat-documents">0</span>
                                        <span class="stat-label">Documentos</span>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">⏱️</div>
                                    <div class="stat-info">
                                        <span class="stat-value" id="stat-hours">0</span>
                                        <span class="stat-label">Horas</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="dashboard-content">
                            <!-- Recent Activity -->
                            <div class="dashboard-section">
                                <div class="section-header">
                                    <h3>Actividad Reciente</h3>
                                    <button class="btn-link" id="view-all-activity">Ver todo</button>
                                </div>
                                <div class="activity-feed" id="recent-activity">
                                    <div class="activity-item">
                                        <div class="activity-icon">📝</div>
                                        <div class="activity-content">
                                            <p class="activity-text">Bienvenido a CoreDesk Framework</p>
                                            <span class="activity-time">Ahora</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Actions -->
                            <div class="dashboard-section">
                                <div class="section-header">
                                    <h3>Acciones Rápidas</h3>
                                </div>
                                <div class="quick-actions">
                                    <button class="quick-action-btn" id="activate-license-btn">
                                        <div class="action-icon">🔐</div>
                                        <span>Activar Licencia</span>
                                    </button>
                                    <button class="quick-action-btn" id="request-trial-btn">
                                        <div class="action-icon">🆓</div>
                                        <span>Solicitar Demo</span>
                                    </button>
                                    <button class="quick-action-btn" id="import-data-btn">
                                        <div class="action-icon">📥</div>
                                        <span>Importar Datos</span>
                                    </button>
                                    <button class="quick-action-btn" id="sync-cloud-btn">
                                        <div class="action-icon">☁️</div>
                                        <span>Sincronizar</span>
                                    </button>
                                </div>
                            </div>

                            <!-- Modules Section -->
                            <div class="dashboard-section modules-section">
                                <div class="section-header">
                                    <h3>Módulos Disponibles</h3>
                                    <div class="module-filters">
                                        <button class="filter-btn active" data-filter="all">Todos</button>
                                        <button class="filter-btn" data-filter="installed">Instalados</button>
                                        <button class="filter-btn" data-filter="active">Activos</button>
                                    </div>
                                </div>
                                <div id="dynamic-module-grid" class="module-grid">
                                    <!-- Initial loading indicator -->
                                    <div class="no-modules-message">
                                        <div class="loading-spinner"></div>
                                        <div class="module-icon-large">📦</div>
                                        <p>Cargando módulos disponibles...</p>
                                        <div class="loading-progress">
                                            <div class="progress-bar">
                                                <div class="progress-fill" style="width: 0%"></div>
                                            </div>
                                            <small>Inicializando sistema de módulos...</small>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Third-Party Module Installation Zone (Hidden by default) -->
                                <div class="module-dropzone-section" style="display: none;">
                                    <div class="section-header">
                                        <h4>Instalación de Módulos de Terceros</h4>
                                        <small>Arrastra archivos .tar.gz o .zip aquí para instalar</small>
                                    </div>
                                    <div id="module-dropzone" class="module-dropzone">
                                        <div class="dropzone-content">
                                            <i class="fas fa-upload dropzone-icon"></i>
                                            <p>Arrastra módulos aquí</p>
                                            <small>Formatos compatibles: .tar.gz, .zip</small>
                                            <div class="dropzone-actions">
                                                <input type="file" id="module-file-input" accept=".tar.gz,.zip" style="display: none;">
                                                <button id="browse-modules-btn" class="browse-btn">
                                                    <i class="fas fa-folder-open"></i> Explorar archivos
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Notifications Panel -->
                            <div class="dashboard-section">
                                <div class="section-header">
                                    <h3>Notificaciones</h3>
                                    <button class="btn-link" id="mark-all-read">Marcar todo como leído</button>
                                </div>
                                <div class="notifications-list" id="notifications-list">
                                    <div class="notification-item">
                                        <div class="notification-icon">ℹ️</div>
                                        <div class="notification-content">
                                            <p>Bienvenido a CoreDesk Framework. Activa tu licencia para acceder a todas las funcionalidades.</p>
                                            <span class="notification-time">Ahora</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right Panel -->
            <div id="right-panel" class="side-panel right-panel hidden">
                <div class="panel-header">
                    <h3 id="right-panel-title">Panel</h3>
                    <button class="panel-close" data-panel="right">×</button>
                </div>
                <div id="right-panel-content" class="panel-content">
                    <!-- Dynamic content -->
                </div>
            </div>
            
        </div>
        
        <!-- Bottom Panel -->
        <div id="bottom-panel" class="bottom-panel hidden">
            <div class="panel-header">
                <h3 id="bottom-panel-title">Terminal</h3>
                <button class="panel-close" data-panel="bottom">×</button>
            </div>
            <div id="bottom-panel-content" class="panel-content">
                <!-- Terminal or other content -->
            </div>
        </div>
        
        <!-- Status Bar -->
        <div id="statusbar" class="status-bar">
            <div class="status-left">
                <span id="current-module" class="status-item">
                    <span class="status-icon">⚙️</span>
                    <span class="status-text">Sin módulo activo</span>
                </span>
                <span id="connection-status" class="status-item">
                    <span class="status-icon offline">🔴</span>
                    <span class="status-text">Offline</span>
                </span>
            </div>
            
            <div class="status-right">
                <span id="license-status" class="status-item">
                    <span class="status-text">Sin licencia</span>
                </span>
                <span id="sync-status" class="status-item sync-status-trigger" title="Estado de sincronización">
                    <span class="status-icon">🔄</span>
                    <span class="status-text">Sin sincronizar</span>
                </span>
            </div>
        </div>
        
    </div>
    
    <!-- Modal container -->
    <div id="modal-container" class="modal-container hidden">
        <!-- Modals will be dynamically inserted here -->
    </div>
    
    <!-- Loading overlay -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p>Inicializando CoreDesk Framework...</p>
        </div>
    </div>
    
    <!-- Core JavaScript files - ORDER MATTERS for dependencies -->
    <script src="js/utils/GlobalLogger.js"></script>
    <script src="js/utils/constants.js"></script>
    <script src="js/utils/events.js"></script>
    <script src="js/utils/Logger.js"></script>
    <script src="js/utils/MemoryManager.js"></script>
    
    <!-- Core security system - load in dependency order -->
    <script src="js/utils/ErrorHandler.js"></script>
    <script src="js/utils/InputValidator.js"></script>
    <script src="js/security/SecurityManager.js"></script>
    
    <!-- Global initialization - must load after core components -->
    <script src="js/utils/GlobalInit.js"></script>
    
    <!-- External Dependencies -->
    <!-- External dependencies (local copy for Electron compatibility) -->
    <script src="js/vendor/axios.min.js"></script>
    <script>
        if (typeof window.axios === 'undefined') {
            console.error('[Critical] Axios failed to load locally, trying CDN fallback...');
            document.write('<script src="https://cdn.jsdelivr.net/npm/axios@1.6.0/dist/axios.min.js"><\/script>');
        } else {
            console.log('[Success] Axios loaded successfully from local file');
        }
    </script>
    
    <!-- API and Authentication Services -->
    <script src="js/services/api/ApiClient.js"></script>
    <script src="js/services/api/AuthApiService.js"></script>
    <script src="js/services/api/LicenseApiService.js"></script>
    <script src="js/auth/SecureTokenManager.js"></script>
    <script src="js/auth/TokenManager.js"></script>
    <script src="js/auth/AuthGuard.js"></script>
    
    <script src="js/utils/IconFallback.js"></script>
    <script src="js/config/authConfig.js"></script>
    
    <!-- Authentication is verified in <head> section before content renders -->
    
    <!-- Core components -->
    <script src="js/components/titleBar.js"></script>
    <script src="js/components/activityBar.js"></script>
    <script src="js/components/FileExplorer.js"></script>
    <script src="js/components/panelManager.js"></script>
    <script src="js/components/statusBar.js"></script>
    <script src="js/components/ThemeManager.js"></script>
    <script src="js/components/AccountModal.js"></script>
    <script src="js/components/PanelResizer.js"></script>
    <script src="js/components/SearchPanel.js"></script>
    <script src="js/components/CloudPanel.js"></script>
    <script src="js/components/NotificationCenter.js"></script>
    <script src="js/components/UpdateDialogs.js"></script>
    <script src="js/config/ConfigurationPanel.js"></script>
    
    <!-- Simplified tab system (5 files) -->
    <script src="js/simplified/SimplifiedTabManager.js"></script>
    <script src="js/simplified/TabContentRenderer.js"></script>
    <script src="js/simplified/TabStateStore.js"></script>
    <script src="js/simplified/TabEventBus.js"></script>
    <script src="js/simplified/SimplifiedTabSystem.js"></script>
    
    <!-- License system -->
    <script src="js/license/DeviceFingerprint.js"></script>
    <script src="js/license/LicenseValidator.js"></script>
    <script src="js/license/LicenseManager.js"></script>
    <script src="js/components/LicenseActivationModal.js"></script>
    
    <!-- Data sync system -->
    <script src="js/services/FirebaseConnector.js"></script>
    <script src="js/services/DataSyncService.js"></script>
    <script src="js/components/SyncStatusPanel.js"></script>
    
    <!-- Dynamic Module System (must load before ExclusiveModuleController) -->
    <script src="js/core/ModulePackage.js"></script>
    <script src="js/core/ModuleRegistry.js"></script>
    <script src="js/services/ModuleDownloader.js"></script>
    <script src="js/services/DynamicStyleLoader.js"></script>
    <script src="js/services/ModulePersistenceClient.js"></script>
    <script src="js/core/DynamicModuleManager.js"></script>
    
    <!-- Core systems -->
    <script src="js/core/ExclusiveModuleController.js"></script>
    <script src="js/auth/UnifiedAuthManager.js"></script>
    
    <!-- Authentication test script (for debugging) -->
    <script src="js/test/testAuth.js"></script>
    
    <!-- Token persistence debug script (temporary for debugging) -->
    <script src="js/test/debugTokenPersistence.js"></script>
    
    <!-- Module packages will be loaded dynamically by DynamicModuleManager -->
    <!-- Static packages removed - modules must be downloaded from server -->
    
    <!-- Application initialization -->
    <script src="js/app.js"></script>
    
</body>
</html>
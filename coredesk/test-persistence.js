#!/usr/bin/env node

/**
 * Test script for module persistence system
 * This script tests the database connection and module persistence functionality
 */

const path = require('path');
const fs = require('fs');
const DatabaseService = require('./src/js/services/DatabaseService');

// Simple logger
const logger = {
    info: (tag, message, data) => console.log(`[INFO] [${tag}] ${message}`, data || ''),
    error: (tag, message, error) => console.error(`[ERROR] [${tag}] ${message}`, error || ''),
    debug: (tag, message, data) => console.log(`[DEBUG] [${tag}] ${message}`, data || '')
};

async function testDatabaseConnection() {
    console.log('🔧 Testing Database Connection...');

    try {
        // Mock app.getPath for testing
        global.app = {
            getPath: (name) => {
                if (name === 'userData') {
                    return path.join(process.env.HOME, '.config', 'coredesk');
                }
                return '/tmp';
            }
        };

        // Initialize database service
        const dbService = new DatabaseService(logger);
        const initialized = await dbService.initialize();

        if (!initialized) {
            throw new Error('Database initialization failed');
        }
        
        console.log('✅ Database initialized successfully');
        
        // Test the execute method
        console.log('🔍 Testing execute method...');
        
        const testQuery = `
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='installed_modules'
        `;
        
        const result = await dbService.execute(testQuery, []);
        console.log('📊 Execute result:', result);
        
        if (result.success && result.data.length > 0) {
            console.log('✅ installed_modules table exists');
        } else {
            console.log('❌ installed_modules table not found');
        }
        
        // Test inserting a test module
        console.log('📝 Testing module registration...');
        
        const insertQuery = `
            INSERT OR REPLACE INTO installed_modules 
            (module_id, name, version, status, install_path, manifest_data)
            VALUES (?, ?, ?, ?, ?, ?)
        `;
        
        const testModuleData = {
            module_id: 'test-module',
            name: 'Test Module',
            version: '1.0.0',
            status: 'active',
            install_path: '/test/path',
            manifest_data: JSON.stringify({
                name: 'Test Module',
                version: '1.0.0',
                description: 'Test module for persistence testing'
            })
        };
        
        const insertResult = await dbService.execute(insertQuery, [
            testModuleData.module_id,
            testModuleData.name,
            testModuleData.version,
            testModuleData.status,
            testModuleData.install_path,
            testModuleData.manifest_data
        ]);
        
        console.log('📊 Insert result:', insertResult);
        
        if (insertResult.success) {
            console.log('✅ Test module registered successfully');
        } else {
            console.log('❌ Failed to register test module');
        }
        
        // Test retrieving the module
        console.log('🔍 Testing module retrieval...');
        
        const selectQuery = `
            SELECT * FROM installed_modules 
            WHERE module_id = ?
        `;
        
        const selectResult = await dbService.execute(selectQuery, ['test-module']);
        console.log('📊 Select result:', selectResult);
        
        if (selectResult.success && selectResult.data.length > 0) {
            console.log('✅ Test module retrieved successfully');
            console.log('📄 Module data:', selectResult.data[0]);
        } else {
            console.log('❌ Failed to retrieve test module');
        }
        
        // Clean up test data
        console.log('🧹 Cleaning up test data...');
        
        const deleteQuery = `
            DELETE FROM installed_modules 
            WHERE module_id = ?
        `;
        
        const deleteResult = await dbService.execute(deleteQuery, ['test-module']);
        console.log('📊 Delete result:', deleteResult);
        
        if (deleteResult.success) {
            console.log('✅ Test data cleaned up successfully');
        } else {
            console.log('❌ Failed to clean up test data');
        }
        
        // Close database connection
        dbService.close();
        console.log('✅ Database connection closed');
        
        console.log('\n🎉 Database persistence test completed successfully!');
        
    } catch (error) {
        console.error('❌ Database test failed:', error);
        process.exit(1);
    }
}

async function testModulesDirectory() {
    console.log('📁 Testing modules directory...');
    
    try {
        const modulesPath = path.join(process.env.HOME, 'coredesk', 'modulos');
        console.log('📍 Modules path:', modulesPath);
        
        // Check if directory exists
        if (fs.existsSync(modulesPath)) {
            console.log('✅ Modules directory exists');
            
            // List contents
            const contents = fs.readdirSync(modulesPath);
            console.log('📄 Directory contents:', contents);
            
            if (contents.length === 0) {
                console.log('ℹ️ Modules directory is empty (expected for fresh installation)');
            } else {
                console.log(`📦 Found ${contents.length} items in modules directory`);
            }
        } else {
            console.log('❌ Modules directory does not exist');
        }
        
    } catch (error) {
        console.error('❌ Modules directory test failed:', error);
    }
}

async function main() {
    console.log('🚀 CoreDesk Module Persistence Test');
    console.log('===================================\n');
    
    await testDatabaseConnection();
    console.log('');
    await testModulesDirectory();
    
    console.log('\n✅ All tests completed!');
}

// Run the test
main().catch(error => {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
});
